-- Consoli-- Set up variables for tenant ID and schema
-- This script requires the following variables to be defined before execution:
-- :setvar TenantId "YOUR_TENANT_ID_NUMBER"
-- :setvar SchemaName "YOUR_SCHEMA_NAME"

-- Declare local variables that will be used throughout the script
DECLARE @TenantId INT;
DECLARE @SchemaName VARCHAR(100);

-- Set the variables using values that should be provided when running the script
-- For manual testing, replace these with actual values
SET @TenantId = CONVERT(INT, '$(TenantId)');
SET @SchemaName = '$(SchemaName)';back Scripts for XT-95683
-- Target: Purchase Invoice 'Dummy 2024-1111' (_id = '81700')
-- IMPORTANT: This script is in MSSQL format. Replace variables before execution.
-- Execute within a transaction if possible.
-- These scripts attempt to revert the changes made by the consolidated operational script.

-- --- From script-rollback-XT95683-005.sql ---
-- Script to rollback XT95683-005
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

-- Set up variables for tenant ID and schema
-- Use SQLCMD variables directly
--:setvar TenantId "INSERT_YOUR_TENANT_ID_HERE"
--:setvar SchemaName "INSERT_YOUR_SCHEMA_NAME_HERE"

DECLARE @TenantId INT;
DECLARE @SchemaName NVARCHAR(100);

SET @TenantId = $(TenantId);
SET @SchemaName = N'$(SchemaName)';

-- Re-insert the deleted accounting_staging record for purchase invoice with _id = '81700'
INSERT INTO [$(SchemaName)].accounting_staging
([_tenant_id], [_id], batch_id, batch_size, base_document_line, source_base_document_line, stock_journal,
document_sys_id, document_number, description, source_document_number, document_date, tax_date, document_type,
source_document_type, movement_type, target_document_type, financial_site, recipient_site, provider_site,
item, account, customer, supplier, pay_to_supplier, pay_to_supplier_linked_address, return_linked_address,
item_posting_class, customer_posting_class, supplier_posting_class, resource_posting_class, resource,
transaction_currency, company_fx_rate, company_fx_rate_divisor, payment_term, due_date, supplier_document_date,
supplier_document_number, is_printed, tax_calculation_status, fx_rate_date, stored_dimensions, stored_attributes,
origin_notification_id, stored_computed_attributes, is_processed, to_be_reprocessed, reply_topic, analytical_data,
[_create_user], [_update_user], [_create_stamp], [_update_stamp], [_update_tick], [_source_id], [_custom_data])
VALUES
(@TenantId, 317363, '401c394c-e60c-4c8d-b230-05a2ea7e13f8', 1, 656996, NULL, NULL,
81700, 'Dummy 2024-1111', '', '', '2025-05-13', '2025-05-13', 'purchaseInvoice',
'materialTracking', 'document', 'accountsPayableInvoice', 180, NULL, NULL,
129396, NULL, NULL, 23542, 23542, NULL, NULL,
1149, NULL, NULL, NULL, NULL,
28, 1.**********, 1.**********, 221, '2025-06-12', '2025-05-13',
'Dummy 2024-1111', 0, NULL, '2025-05-13', '{}', N'{"supplier": "AG00001", "stockSite": "WCT", "businessSite": "WCT", "financialSite": "WCT"}',
'cEyFwQrNakLvBPqMxyOoD', '{}', 0, 0, 'PurchaseInvoice/accountingInterface', 136666,
640, 645, '2025-05-13 23:31:11.300', '2025-05-28 00:00:12.732', 19, '', '{}');

-- Re-insert the deleted accounting_staging_amount record
INSERT INTO [$(SchemaName)].accounting_staging_amount
([_tenant_id], [_id], [_sort_value], accounting_staging, amount_type, amount, tax, tax_posting_class,
base_tax, document_line_type, tax_date, tax_rate, [_create_user], [_update_user],
[_create_stamp], [_update_stamp], [_update_tick], [_source_id], [_custom_data])
VALUES
(@TenantId, 503124, 10, 317363, 'amountExcludingTax', 590.**********, NULL, NULL,
NULL, 'documentLine', NULL, 0.**********, 640, 645,
'2025-05-13 23:31:11.300', '2025-05-28 00:00:12.732', 19, '', '{}');

-- Re-insert the deleted accounting_staging_line_tax records
INSERT INTO [$(SchemaName)].accounting_staging_line_tax
([_tenant_id], [_id], [_sort_value], accounting_staging, base_tax, [_create_user], [_update_user],
[_create_stamp], [_update_stamp], [_update_tick], [_source_id], [_custom_data])
VALUES
(@TenantId, 152700, 10, 317363, 983679, 640, 645,
'2025-05-13 23:31:11.300', '2025-05-28 00:00:12.732', 19, '', '{}');

INSERT INTO [$(SchemaName)].accounting_staging_line_tax
([_tenant_id], [_id], [_sort_value], accounting_staging, base_tax, [_create_user], [_update_user],
[_create_stamp], [_update_stamp], [_update_tick], [_source_id], [_custom_data])
VALUES
(@TenantId, 152701, 20, 317363, 983680, 640, 645,
'2025-05-13 23:31:11.300', '2025-05-28 00:00:12.732', 19, '', '{}');

INSERT INTO [$(SchemaName)].accounting_staging_line_tax
([_tenant_id], [_id], [_sort_value], accounting_staging, base_tax, [_create_user], [_update_user],
[_create_stamp], [_update_stamp], [_update_tick], [_source_id], [_custom_data])
VALUES
(@TenantId, 152702, 30, 317363, 983681, 640, 645,
'2025-05-13 23:31:11.300', '2025-05-28 00:00:12.732', 19, '', '{}');

INSERT INTO [$(SchemaName)].accounting_staging_line_tax
([_tenant_id], [_id], [_sort_value], accounting_staging, base_tax, [_create_user], [_update_user],
[_create_stamp], [_update_stamp], [_update_tick], [_source_id], [_custom_data])
VALUES
(@TenantId, 152703, 40, 317363, 983682, 640, 645,
'2025-05-13 23:31:11.300', '2025-05-28 00:00:12.732', 19, '', '{}');

-- Return a message indicating successful rollback
SELECT 'Accounting staging records for purchase invoice "Dummy 2024-1111" with document_sys_id = 81700 have been restored.' AS message;

-- --- From script-rollback-XT95683-004.sql ---
-- Script to rollback XT95683-004
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice with _id = '81700'
-- This script re-inserts the finance transaction record that was deleted

-- Re-insert the deleted finance transaction record based on backup data
INSERT INTO [$(SchemaName)].finance_transaction
([_tenant_id], [_id], batch_id, document_number, document_sys_id, document_type, target_document_type,
target_document_number, target_document_sys_id, source_document_type, source_document_number,
source_document_sys_id, financial_site, status, finance_integration_app, finance_integration_app_record_id,
finance_integration_app_url, message, last_status_update, [_create_user], [_update_user],
[_create_stamp], [_update_stamp], [_update_tick], [_source_id], [_custom_data])
VALUES
(@TenantId, '125830', '401c394c-e60c-4c8d-b230-05a2ea7e13f8', 'Dummy 2024-1111', '81700',
'purchaseInvoice', 'accountsPayableInvoice', '', 0,
'materialTracking', '', 0, 180, 'notRecorded',
NULL, '', '', N'[{"type":3,"message":{"message":"value too long for type character varying(30)","extensions":{"code":"system-error"}}}]',
'2025-05-13 23:31:14', 640, 645,
'2025-05-13 23:31:11.3', '2025-05-28 00:00:12.732', 19, '', '{}');

-- Return a message indicating successful rollback
SELECT 'Finance transaction record with _id = ''125830'' for purchase invoice 81700 has been restored.' AS message;

-- --- From script-rollback-XT95683-003.sql ---
-- Script to rollback XT95683-003
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

UPDATE [$(SchemaName)].purchase_invoice
SET finance_status = 'error',
    posting_status = 'error',
    tax_calculation_status = 'error'
WHERE [_tenant_id] = @TenantId
AND [_id] = '81700';

-- --- From script-rollback-XT95683-002.sql ---
-- Script to rollback XT95683-002
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

UPDATE [$(SchemaName)].base_document
SET status = 'error', display_status = 'postingError'
WHERE [_tenant_id] = @TenantId
AND [_id] IN (
    SELECT base_document
    FROM [$(SchemaName)].purchase_invoice
    WHERE [_tenant_id] = @TenantId AND [_id] = '81700'
);

-- --- From script-rollback-XT95683-001.sql ---
-- Script to rollback XT95683-001
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

UPDATE [$(SchemaName)].purchase_invoice_line
SET stock_transaction_status = 'completed'
WHERE [_tenant_id] = @TenantId
AND purchase_invoice IN (
    SELECT [_id]
    FROM [$(SchemaName)].purchase_invoice
    WHERE [_tenant_id] = @TenantId AND [_id] = '81700'
);

-- End of Consolidated Rollback Scripts for XT-95683
