-- ================================================================================
-- ENHANCED CONSOLIDATED OPERATIONAL SCRIPTS FOR XT-95683
-- ================================================================================
-- Target: Purchase Invoice 'Dummy 2024-1111' (_id = '81700')
-- IMPORTANT: Replace %%SCHEMA_NAME%% and %%TENANT_ID%% with actual values before execution.
--
-- This script includes comprehensive logging, monitoring, and safety features:
-- - Pre-execution validation
-- - Progress tracking with timestamps
-- - Row count monitoring
-- - Error handling with rollback capability
-- - Post-execution verification
-- ================================================================================

DO $$
DECLARE
    v_start_time TIMESTAMP;
    v_step_start_time TIMESTAMP;
    v_rows_affected INTEGER;
    v_total_rows_affected INTEGER := 0;
    v_purchase_invoice_exists BOOLEAN := FALSE;
    v_base_document_id TEXT;
    v_step_counter INTEGER := 1;
    v_total_steps INTEGER := 5;
    v_error_message TEXT;
BEGIN
    -- Initialize timing
    v_start_time := clock_timestamp();

    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'STARTING ENHANCED XT-95683 DATA FIX SCRIPT';
    RAISE NOTICE 'Target Purchase Invoice ID: 81700';
    RAISE NOTICE 'Execution started at: %', v_start_time;
    RAISE NOTICE 'Total steps to execute: %', v_total_steps;
    RAISE NOTICE '================================================================================';

    -- ============================================================================
    -- PRE-EXECUTION VALIDATION
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '--- PRE-EXECUTION VALIDATION ---';
    v_step_start_time := clock_timestamp();

    -- Validate purchase invoice exists
    SELECT EXISTS(
        SELECT 1 FROM %%SCHEMA_NAME%%.purchase_invoice
        WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700'
    ) INTO v_purchase_invoice_exists;

    IF NOT v_purchase_invoice_exists THEN
        RAISE EXCEPTION 'VALIDATION FAILED: Purchase invoice with ID 81700 not found in tenant %%TENANT_ID%%';
    END IF;

    -- Get base document ID for validation
    SELECT base_document INTO v_base_document_id
    FROM %%SCHEMA_NAME%%.purchase_invoice
    WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700';

    RAISE NOTICE 'Validation completed successfully:';
    RAISE NOTICE '  - Purchase invoice 81700 exists: %', v_purchase_invoice_exists;
    RAISE NOTICE '  - Associated base document ID: %', COALESCE(v_base_document_id, 'NULL');
    RAISE NOTICE '  - Validation duration: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_step_start_time);

    -- ============================================================================
    -- STEP 1/5: UPDATE PURCHASE INVOICE LINE STOCK TRANSACTION STATUS
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'STEP %/% - UPDATING PURCHASE INVOICE LINE STOCK TRANSACTION STATUS', v_step_counter, v_total_steps;
    RAISE NOTICE '================================================================================';
    v_step_start_time := clock_timestamp();

    BEGIN
        RAISE NOTICE 'Executing: UPDATE purchase_invoice_line SET stock_transaction_status = ''draft''';
        RAISE NOTICE 'Target: Lines associated with purchase invoice 81700';

        UPDATE %%SCHEMA_NAME%%.purchase_invoice_line
        SET stock_transaction_status = 'draft'
        WHERE "_tenant_id" = %%TENANT_ID%%
        AND purchase_invoice IN (
            SELECT "_id"
            FROM %%SCHEMA_NAME%%.purchase_invoice
            WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700'
        );

        GET DIAGNOSTICS v_rows_affected = ROW_COUNT;
        v_total_rows_affected := v_total_rows_affected + v_rows_affected;

        RAISE NOTICE 'Step % completed successfully:', v_step_counter;
        RAISE NOTICE '  - Rows affected: %', v_rows_affected;
        RAISE NOTICE '  - Step duration: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_step_start_time);

        -- Show affected records
        IF v_rows_affected > 0 THEN
            RAISE NOTICE 'Updated purchase invoice lines:';
            FOR v_error_message IN
                SELECT '  - Line ID: ' || _id || ', Purchase Invoice: ' || purchase_invoice || ', Status: ' || stock_transaction_status
                FROM %%SCHEMA_NAME%%.purchase_invoice_line
                WHERE "_tenant_id" = %%TENANT_ID%%
                AND purchase_invoice = '81700'
                LIMIT 10
            LOOP
                RAISE NOTICE '%', v_error_message;
            END LOOP;
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            v_error_message := SQLERRM;
            RAISE EXCEPTION 'STEP % FAILED - Purchase Invoice Line Update: %', v_step_counter, v_error_message;
    END;

    v_step_counter := v_step_counter + 1;

    -- ============================================================================
    -- STEP 2/5: UPDATE BASE DOCUMENT STATUS
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'STEP %/% - UPDATING BASE DOCUMENT STATUS', v_step_counter, v_total_steps;
    RAISE NOTICE '================================================================================';
    v_step_start_time := clock_timestamp();

    BEGIN
        RAISE NOTICE 'Executing: UPDATE base_document SET status = ''draft'', display_status = ''noVariance''';
        RAISE NOTICE 'Target: Base document associated with purchase invoice 81700';

        UPDATE %%SCHEMA_NAME%%.base_document
        SET status = 'draft', display_status = 'noVariance'
        WHERE "_tenant_id" = %%TENANT_ID%%
        AND "_id" IN (
            SELECT base_document
            FROM %%SCHEMA_NAME%%.purchase_invoice
            WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700'
        );

        GET DIAGNOSTICS v_rows_affected = ROW_COUNT;
        v_total_rows_affected := v_total_rows_affected + v_rows_affected;

        RAISE NOTICE 'Step % completed successfully:', v_step_counter;
        RAISE NOTICE '  - Rows affected: %', v_rows_affected;
        RAISE NOTICE '  - Step duration: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_step_start_time);

        -- Show affected records
        IF v_rows_affected > 0 THEN
            RAISE NOTICE 'Updated base documents:';
            FOR v_error_message IN
                SELECT '  - Document ID: ' || _id || ', Status: ' || status || ', Display Status: ' || display_status
                FROM %%SCHEMA_NAME%%.base_document
                WHERE "_tenant_id" = %%TENANT_ID%%
                AND "_id" = v_base_document_id
            LOOP
                RAISE NOTICE '%', v_error_message;
            END LOOP;
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            v_error_message := SQLERRM;
            RAISE EXCEPTION 'STEP % FAILED - Base Document Update: %', v_step_counter, v_error_message;
    END;

    v_step_counter := v_step_counter + 1;

    -- ============================================================================
    -- STEP 3/5: UPDATE PURCHASE INVOICE FINANCE STATUS
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'STEP %/% - UPDATING PURCHASE INVOICE FINANCE STATUS', v_step_counter, v_total_steps;
    RAISE NOTICE '================================================================================';
    v_step_start_time := clock_timestamp();

    BEGIN
        RAISE NOTICE 'Executing: UPDATE purchase_invoice SET finance_status = ''draft'', posting_status = ''draft'', tax_calculation_status = ''pending''';
        RAISE NOTICE 'Target: Purchase invoice 81700';

        UPDATE %%SCHEMA_NAME%%.purchase_invoice
        SET finance_status = 'draft',
            posting_status = 'draft',
            tax_calculation_status = 'pending'
        WHERE "_tenant_id" = %%TENANT_ID%%
        AND "_id" = '81700';

        GET DIAGNOSTICS v_rows_affected = ROW_COUNT;
        v_total_rows_affected := v_total_rows_affected + v_rows_affected;

        RAISE NOTICE 'Step % completed successfully:', v_step_counter;
        RAISE NOTICE '  - Rows affected: %', v_rows_affected;
        RAISE NOTICE '  - Step duration: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_step_start_time);

        -- Show affected records
        IF v_rows_affected > 0 THEN
            RAISE NOTICE 'Updated purchase invoice:';
            FOR v_error_message IN
                SELECT '  - Invoice ID: ' || _id || ', Number: ' || COALESCE(number, 'NULL') || ', Finance Status: ' || finance_status || ', Posting Status: ' || posting_status
                FROM %%SCHEMA_NAME%%.purchase_invoice
                WHERE "_tenant_id" = %%TENANT_ID%%
                AND "_id" = '81700'
            LOOP
                RAISE NOTICE '%', v_error_message;
            END LOOP;
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            v_error_message := SQLERRM;
            RAISE EXCEPTION 'STEP % FAILED - Purchase Invoice Finance Status Update: %', v_step_counter, v_error_message;
    END;

    v_step_counter := v_step_counter + 1;

    -- ============================================================================
    -- STEP 4/5: DELETE FINANCE TRANSACTIONS
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'STEP %/% - DELETING FINANCE TRANSACTIONS', v_step_counter, v_total_steps;
    RAISE NOTICE '================================================================================';
    v_step_start_time := clock_timestamp();

    BEGIN
        RAISE NOTICE 'Executing: DELETE FROM finance_transaction WHERE document_sys_id = ''81700''';
        RAISE NOTICE 'Target: Finance transactions for purchase invoice 81700';

        DELETE FROM %%SCHEMA_NAME%%.finance_transaction
        WHERE "_tenant_id" = %%TENANT_ID%% AND "document_sys_id" = '81700';

        GET DIAGNOSTICS v_rows_affected = ROW_COUNT;
        v_total_rows_affected := v_total_rows_affected + v_rows_affected;

        RAISE NOTICE 'Step % completed successfully:', v_step_counter;
        RAISE NOTICE '  - Rows affected: %', v_rows_affected;
        RAISE NOTICE '  - Step duration: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_step_start_time);

        IF v_rows_affected > 0 THEN
            RAISE NOTICE 'Deleted % finance transaction(s) for document 81700', v_rows_affected;
        ELSE
            RAISE NOTICE 'No finance transactions found for document 81700 (this may be expected)';
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            v_error_message := SQLERRM;
            RAISE EXCEPTION 'STEP % FAILED - Finance Transaction Deletion: %', v_step_counter, v_error_message;
    END;

    v_step_counter := v_step_counter + 1;

    -- ============================================================================
    -- STEP 5/5: DELETE ACCOUNTING STAGING RECORDS
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'STEP %/% - DELETING ACCOUNTING STAGING RECORDS', v_step_counter, v_total_steps;
    RAISE NOTICE '================================================================================';
    v_step_start_time := clock_timestamp();

    BEGIN
        RAISE NOTICE 'Executing: DELETE FROM accounting_staging WHERE document_sys_id = ''81700''';
        RAISE NOTICE 'Target: Accounting staging records for purchase invoice 81700';

        DELETE FROM %%SCHEMA_NAME%%.accounting_staging
        WHERE "_tenant_id" = %%TENANT_ID%%
        AND document_type = 'purchaseInvoice'
        AND document_sys_id = '81700';

        GET DIAGNOSTICS v_rows_affected = ROW_COUNT;
        v_total_rows_affected := v_total_rows_affected + v_rows_affected;

        RAISE NOTICE 'Step % completed successfully:', v_step_counter;
        RAISE NOTICE '  - Rows affected: %', v_rows_affected;
        RAISE NOTICE '  - Step duration: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_step_start_time);

        IF v_rows_affected > 0 THEN
            RAISE NOTICE 'Deleted % accounting staging record(s) for document 81700', v_rows_affected;
        ELSE
            RAISE NOTICE 'No accounting staging records found for document 81700 (this may be expected)';
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            v_error_message := SQLERRM;
            RAISE EXCEPTION 'STEP % FAILED - Accounting Staging Deletion: %', v_step_counter, v_error_message;
    END;

    -- ============================================================================
    -- POST-EXECUTION VERIFICATION
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'POST-EXECUTION VERIFICATION';
    RAISE NOTICE '================================================================================';
    v_step_start_time := clock_timestamp();

    -- Verify purchase invoice still exists but with updated statuses
    SELECT EXISTS(
        SELECT 1 FROM %%SCHEMA_NAME%%.purchase_invoice
        WHERE "_tenant_id" = %%TENANT_ID%%
        AND "_id" = '81700'
        AND finance_status = 'draft'
        AND posting_status = 'draft'
    ) INTO v_purchase_invoice_exists;

    RAISE NOTICE 'Verification Results:';
    RAISE NOTICE '  - Purchase invoice 81700 exists with draft status: %', v_purchase_invoice_exists;

    -- Check remaining related records
    SELECT COUNT(*) INTO v_rows_affected
    FROM %%SCHEMA_NAME%%.purchase_invoice_line
    WHERE "_tenant_id" = %%TENANT_ID%%
    AND purchase_invoice = '81700'
    AND stock_transaction_status = 'draft';

    RAISE NOTICE '  - Purchase invoice lines with draft status: %', v_rows_affected;

    SELECT COUNT(*) INTO v_rows_affected
    FROM %%SCHEMA_NAME%%.finance_transaction
    WHERE "_tenant_id" = %%TENANT_ID%% AND "document_sys_id" = '81700';

    RAISE NOTICE '  - Remaining finance transactions: %', v_rows_affected;

    SELECT COUNT(*) INTO v_rows_affected
    FROM %%SCHEMA_NAME%%.accounting_staging
    WHERE "_tenant_id" = %%TENANT_ID%%
    AND document_type = 'purchaseInvoice'
    AND document_sys_id = '81700';

    RAISE NOTICE '  - Remaining accounting staging records: %', v_rows_affected;
    RAISE NOTICE '  - Verification duration: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_step_start_time);

    -- ============================================================================
    -- EXECUTION SUMMARY
    -- ============================================================================
    RAISE NOTICE '';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'EXECUTION SUMMARY';
    RAISE NOTICE '================================================================================';
    RAISE NOTICE 'Script execution completed successfully!';
    RAISE NOTICE 'Target Purchase Invoice ID: 81700';
    RAISE NOTICE 'Total steps executed: %', v_total_steps;
    RAISE NOTICE 'Total rows affected across all operations: %', v_total_rows_affected;
    RAISE NOTICE 'Total execution time: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_start_time);
    RAISE NOTICE 'Execution completed at: %', clock_timestamp();
    RAISE NOTICE '';
    RAISE NOTICE 'All operations completed successfully. The purchase invoice 81700 has been';
    RAISE NOTICE 'reset to draft status and associated finance/accounting records have been cleaned up.';
    RAISE NOTICE '================================================================================';

EXCEPTION
    WHEN OTHERS THEN
        -- Global error handler
        RAISE NOTICE '';
        RAISE NOTICE '================================================================================';
        RAISE NOTICE 'SCRIPT EXECUTION FAILED';
        RAISE NOTICE '================================================================================';
        RAISE NOTICE 'Error occurred during execution:';
        RAISE NOTICE 'Error Message: %', SQLERRM;
        RAISE NOTICE 'Error Detail: %', SQLSTATE;
        RAISE NOTICE 'Execution time before failure: % ms', EXTRACT(MILLISECONDS FROM clock_timestamp() - v_start_time);
        RAISE NOTICE '';
        RAISE NOTICE 'IMPORTANT: All changes have been rolled back due to the error.';
        RAISE NOTICE 'Please review the error message and fix any issues before re-running.';
        RAISE NOTICE '================================================================================';

        -- Re-raise the exception to ensure transaction rollback
        RAISE;
END $$;
