-- Consolidated Operational Scripts for XT-95683
-- Target: Purchase Invoice 'Dummy 2024-1111' (_id = '81700')
-- IMPORTANT: Replace %%SCHEMA_NAME%% and %%TENANT_ID%% with actual values before execution.
-- Execute within a transaction if possible.

-- --- From script-XT95683-001.sql ---
-- <PERSON><PERSON><PERSON> to update stock transaction status to 'draft' for purchase invoice lines
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

UPDATE %%SCHEMA_NAME%%.purchase_invoice_line
SET stock_transaction_status = 'draft'
WHERE "_tenant_id" = %%TENANT_ID%%
AND purchase_invoice IN (
    SELECT "_id"
    FROM %%SCHEMA_NAME%%.purchase_invoice
    WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700'
)
RETURNING _id, purchase_invoice, stock_transaction_status;

-- --- From script-XT95683-002.sql ---
-- <PERSON><PERSON><PERSON> to update base document status to 'draft' and display status to 'noVariance'
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

UPDATE %%SCHEMA_NAME%%.base_document
SET status = 'draft', display_status = 'noVariance'
WHERE "_tenant_id" = %%TENANT_ID%%
AND "_id" IN (
    SELECT base_document
    FROM %%SCHEMA_NAME%%.purchase_invoice
    WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700'
)
RETURNING _id, status, display_status;

-- --- From script-XT95683-003.sql ---
-- Script to update finance status to 'draft' for purchase invoice
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

UPDATE %%SCHEMA_NAME%%.purchase_invoice
SET finance_status = 'draft',
    posting_status = 'draft',
    tax_calculation_status = 'pending'
WHERE "_tenant_id" = %%TENANT_ID%%
AND "_id" = '81700'
RETURNING _id, number, finance_status, posting_status;

-- --- From script-XT95683-004.sql (Corrected Version) ---
-- Script to delete finance transaction for purchase invoice 'Dummy 2024-1111' (_id = '81700')
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"
-- Corrected to target document_sys_id = '81700'

DELETE FROM %%SCHEMA_NAME%%.finance_transaction
WHERE "_tenant_id" =  %%TENANT_ID%% AND "document_sys_id"= '81700'
returning _id;

-- --- From script-XT95683-005.sql ---
-- Script to delete accounting staging records for purchase invoice
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"

DELETE FROM %%SCHEMA_NAME%%.accounting_staging
WHERE "_tenant_id" = %%TENANT_ID%%
AND document_type = 'purchaseInvoice'
AND document_sys_id = '81700'
RETURNING _id, document_number;

-- End of Consolidated Operational Scripts for XT-95683
