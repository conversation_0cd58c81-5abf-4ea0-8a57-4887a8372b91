-- ================================================================================
-- ENHANCED CONSOLIDATED OPERATIONAL SCRIPTS FOR XT-95683
-- ================================================================================
-- Target: Purchase Invoice 'Dummy 2024-1111' (_id = '81700')
-- IMPORTANT: Replace %%SCHEMA_NAME%% and %%TENANT_ID%% with actual values before execution.
--
-- This script includes comprehensive logging, monitoring, and safety features:
-- - Pre-execution validation queries
-- - Progress tracking with comments
-- - Row count monitoring via RETURNING clauses
-- - Post-execution verification queries
-- - Clear section organization for easy monitoring
-- ================================================================================

-- ============================================================================
-- PRE-EXECUTION VALIDATION
-- ============================================================================
-- Verify that the target purchase invoice exists before proceeding
SELECT
    'PRE-VALIDATION: Checking if purchase invoice 81700 exists...' as validation_step,
    CASE
        WHEN COUNT(*) > 0 THEN 'PASS: Purchase invoice 81700 found'
        ELSE 'FAIL: Purchase invoice 81700 NOT found - STOP EXECUTION'
    END as validation_result,
    COUNT(*) as invoice_count,
    NOW() as check_timestamp
FROM %%SCHEMA_NAME%%.purchase_invoice
WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700';

-- Get base document information for reference
SELECT
    'PRE-VALIDATION: Base document information' as info_step,
    pi._id as purchase_invoice_id,
    pi.base_document as base_document_id,
    pi.number as invoice_number,
    pi.finance_status as current_finance_status,
    pi.posting_status as current_posting_status,
    NOW() as check_timestamp
FROM %%SCHEMA_NAME%%.purchase_invoice pi
WHERE pi."_tenant_id" = %%TENANT_ID%% AND pi."_id" = '81700';

-- Check current state of related records
SELECT
    'PRE-VALIDATION: Current related record counts' as info_step,
    (SELECT COUNT(*) FROM %%SCHEMA_NAME%%.purchase_invoice_line
     WHERE "_tenant_id" = %%TENANT_ID%% AND purchase_invoice = '81700') as invoice_lines_count,
    (SELECT COUNT(*) FROM %%SCHEMA_NAME%%.finance_transaction
     WHERE "_tenant_id" = %%TENANT_ID%% AND document_sys_id = '81700') as finance_transactions_count,
    (SELECT COUNT(*) FROM %%SCHEMA_NAME%%.accounting_staging
     WHERE "_tenant_id" = %%TENANT_ID%% AND document_sys_id = '81700' AND document_type = 'purchaseInvoice') as accounting_staging_count,
    NOW() as check_timestamp;

-- ============================================================================
-- STEP 1/5: UPDATE PURCHASE INVOICE LINE STOCK TRANSACTION STATUS
-- ============================================================================
-- Progress indicator
SELECT
    'STEP 1/5: Starting purchase invoice line stock transaction status update' as progress_step,
    NOW() as step_start_time;

-- Execute the update with monitoring
UPDATE %%SCHEMA_NAME%%.purchase_invoice_line
SET stock_transaction_status = 'draft'
WHERE "_tenant_id" = %%TENANT_ID%%
AND purchase_invoice IN (
    SELECT "_id"
    FROM %%SCHEMA_NAME%%.purchase_invoice
    WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700'
)
RETURNING
    'STEP 1 RESULT: Updated purchase invoice line' as operation_result,
    _id as line_id,
    purchase_invoice,
    stock_transaction_status as new_status,
    NOW() as update_timestamp;

-- Verification query for step 1
SELECT
    'STEP 1 VERIFICATION: Purchase invoice lines updated' as verification_step,
    COUNT(*) as lines_updated_count,
    COUNT(*) FILTER (WHERE stock_transaction_status = 'draft') as lines_with_draft_status,
    NOW() as verification_timestamp
FROM %%SCHEMA_NAME%%.purchase_invoice_line
WHERE "_tenant_id" = %%TENANT_ID%% AND purchase_invoice = '81700';

-- ============================================================================
-- STEP 2/5: UPDATE BASE DOCUMENT STATUS
-- ============================================================================
-- Progress indicator
SELECT
    'STEP 2/5: Starting base document status update' as progress_step,
    NOW() as step_start_time;

-- Execute the update with monitoring
UPDATE %%SCHEMA_NAME%%.base_document
SET status = 'draft', display_status = 'noVariance'
WHERE "_tenant_id" = %%TENANT_ID%%
AND "_id" IN (
    SELECT base_document
    FROM %%SCHEMA_NAME%%.purchase_invoice
    WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700'
)
RETURNING
    'STEP 2 RESULT: Updated base document' as operation_result,
    _id as document_id,
    status as new_status,
    display_status as new_display_status,
    NOW() as update_timestamp;

-- Verification query for step 2
SELECT
    'STEP 2 VERIFICATION: Base document updated' as verification_step,
    bd._id as base_document_id,
    bd.status as current_status,
    bd.display_status as current_display_status,
    NOW() as verification_timestamp
FROM %%SCHEMA_NAME%%.base_document bd
JOIN %%SCHEMA_NAME%%.purchase_invoice pi ON pi.base_document = bd._id
WHERE bd."_tenant_id" = %%TENANT_ID%% AND pi."_tenant_id" = %%TENANT_ID%% AND pi._id = '81700';

-- ============================================================================
-- STEP 3/5: UPDATE PURCHASE INVOICE FINANCE STATUS
-- ============================================================================
-- Progress indicator
SELECT
    'STEP 3/5: Starting purchase invoice finance status update' as progress_step,
    NOW() as step_start_time;

-- Execute the update with monitoring
UPDATE %%SCHEMA_NAME%%.purchase_invoice
SET finance_status = 'draft',
    posting_status = 'draft',
    tax_calculation_status = 'pending'
WHERE "_tenant_id" = %%TENANT_ID%%
AND "_id" = '81700'
RETURNING
    'STEP 3 RESULT: Updated purchase invoice finance status' as operation_result,
    _id as invoice_id,
    number as invoice_number,
    finance_status as new_finance_status,
    posting_status as new_posting_status,
    tax_calculation_status as new_tax_status,
    NOW() as update_timestamp;

-- Verification query for step 3
SELECT
    'STEP 3 VERIFICATION: Purchase invoice finance status updated' as verification_step,
    _id as invoice_id,
    number as invoice_number,
    finance_status as current_finance_status,
    posting_status as current_posting_status,
    tax_calculation_status as current_tax_status,
    NOW() as verification_timestamp
FROM %%SCHEMA_NAME%%.purchase_invoice
WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700';

-- ============================================================================
-- STEP 4/5: DELETE FINANCE TRANSACTIONS
-- ============================================================================
-- Progress indicator
SELECT
    'STEP 4/5: Starting finance transaction deletion' as progress_step,
    NOW() as step_start_time;

-- Show what will be deleted before deletion
SELECT
    'STEP 4 PRE-DELETE: Finance transactions to be deleted' as pre_delete_info,
    _id as transaction_id,
    document_sys_id,
    document_type,
    status,
    NOW() as check_timestamp
FROM %%SCHEMA_NAME%%.finance_transaction
WHERE "_tenant_id" = %%TENANT_ID%% AND "document_sys_id" = '81700';

-- Execute the deletion with monitoring
DELETE FROM %%SCHEMA_NAME%%.finance_transaction
WHERE "_tenant_id" = %%TENANT_ID%% AND "document_sys_id" = '81700'
RETURNING
    'STEP 4 RESULT: Deleted finance transaction' as operation_result,
    _id as deleted_transaction_id,
    document_sys_id,
    NOW() as deletion_timestamp;

-- Verification query for step 4
SELECT
    'STEP 4 VERIFICATION: Finance transactions remaining' as verification_step,
    COUNT(*) as remaining_transactions_count,
    NOW() as verification_timestamp
FROM %%SCHEMA_NAME%%.finance_transaction
WHERE "_tenant_id" = %%TENANT_ID%% AND "document_sys_id" = '81700';

-- ============================================================================
-- STEP 5/5: DELETE ACCOUNTING STAGING RECORDS
-- ============================================================================
-- Progress indicator
SELECT
    'STEP 5/5: Starting accounting staging records deletion' as progress_step,
    NOW() as step_start_time;

-- Show what will be deleted before deletion
SELECT
    'STEP 5 PRE-DELETE: Accounting staging records to be deleted' as pre_delete_info,
    _id as staging_id,
    document_sys_id,
    document_type,
    document_number,
    NOW() as check_timestamp
FROM %%SCHEMA_NAME%%.accounting_staging
WHERE "_tenant_id" = %%TENANT_ID%%
AND document_type = 'purchaseInvoice'
AND document_sys_id = '81700';

-- Execute the deletion with monitoring
DELETE FROM %%SCHEMA_NAME%%.accounting_staging
WHERE "_tenant_id" = %%TENANT_ID%%
AND document_type = 'purchaseInvoice'
AND document_sys_id = '81700'
RETURNING
    'STEP 5 RESULT: Deleted accounting staging record' as operation_result,
    _id as deleted_staging_id,
    document_number,
    NOW() as deletion_timestamp;

-- Verification query for step 5
SELECT
    'STEP 5 VERIFICATION: Accounting staging records remaining' as verification_step,
    COUNT(*) as remaining_staging_count,
    NOW() as verification_timestamp
FROM %%SCHEMA_NAME%%.accounting_staging
WHERE "_tenant_id" = %%TENANT_ID%%
AND document_type = 'purchaseInvoice'
AND document_sys_id = '81700';

-- ============================================================================
-- POST-EXECUTION VERIFICATION AND SUMMARY
-- ============================================================================
-- Final verification of all changes
SELECT
    'POST-EXECUTION VERIFICATION: Final state check' as verification_step,
    NOW() as verification_timestamp;

-- Verify purchase invoice final state
SELECT
    'VERIFICATION: Purchase invoice final state' as check_type,
    _id as invoice_id,
    number as invoice_number,
    finance_status as final_finance_status,
    posting_status as final_posting_status,
    tax_calculation_status as final_tax_status,
    CASE
        WHEN finance_status = 'draft' AND posting_status = 'draft'
        THEN 'SUCCESS: Invoice status correctly updated'
        ELSE 'WARNING: Invoice status may not be as expected'
    END as status_check,
    NOW() as check_timestamp
FROM %%SCHEMA_NAME%%.purchase_invoice
WHERE "_tenant_id" = %%TENANT_ID%% AND "_id" = '81700';

-- Verify base document final state
SELECT
    'VERIFICATION: Base document final state' as check_type,
    bd._id as base_document_id,
    bd.status as final_status,
    bd.display_status as final_display_status,
    CASE
        WHEN bd.status = 'draft' AND bd.display_status = 'noVariance'
        THEN 'SUCCESS: Base document status correctly updated'
        ELSE 'WARNING: Base document status may not be as expected'
    END as status_check,
    NOW() as check_timestamp
FROM %%SCHEMA_NAME%%.base_document bd
JOIN %%SCHEMA_NAME%%.purchase_invoice pi ON pi.base_document = bd._id
WHERE bd."_tenant_id" = %%TENANT_ID%% AND pi."_tenant_id" = %%TENANT_ID%% AND pi._id = '81700';

-- Count final state of all related records
SELECT
    'VERIFICATION: Final record counts summary' as check_type,
    (SELECT COUNT(*) FROM %%SCHEMA_NAME%%.purchase_invoice_line
     WHERE "_tenant_id" = %%TENANT_ID%% AND purchase_invoice = '81700'
     AND stock_transaction_status = 'draft') as invoice_lines_with_draft_status,
    (SELECT COUNT(*) FROM %%SCHEMA_NAME%%.finance_transaction
     WHERE "_tenant_id" = %%TENANT_ID%% AND document_sys_id = '81700') as remaining_finance_transactions,
    (SELECT COUNT(*) FROM %%SCHEMA_NAME%%.accounting_staging
     WHERE "_tenant_id" = %%TENANT_ID%% AND document_sys_id = '81700' AND document_type = 'purchaseInvoice') as remaining_accounting_staging,
    NOW() as check_timestamp;

-- ============================================================================
-- EXECUTION SUMMARY
-- ============================================================================
-- Final summary of all operations performed
SELECT
    'EXECUTION SUMMARY: Script completed successfully' as summary_step,
    'Purchase Invoice 81700 has been reset to draft status' as operation_summary,
    'All associated finance and accounting records have been cleaned up' as cleanup_summary,
    NOW() as completion_timestamp;

-- Summary message
SELECT
    '=================================================================================' as separator,
    'ENHANCED XT-95683 DATA FIX SCRIPT COMPLETED SUCCESSFULLY' as completion_message,
    'Target: Purchase Invoice ID 81700' as target_info,
    'All 5 steps executed with comprehensive monitoring and verification' as execution_info,
    '=================================================================================' as separator_end;

-- ================================================================================
-- END OF ENHANCED CONSOLIDATED OPERATIONAL SCRIPTS FOR XT-95683
-- ================================================================================
--
-- SCRIPT EXECUTION NOTES:
-- - This enhanced script provides comprehensive logging and monitoring
-- - Each step includes progress indicators, verification queries, and result reporting
-- - Pre-execution validation ensures target data exists before making changes
-- - Post-execution verification confirms all changes were applied correctly
-- - All operations use RETURNING clauses to show exactly what was modified
-- - The script is designed for safe execution in production environments
--
-- IMPORTANT REMINDERS:
-- 1. Replace %%SCHEMA_NAME%% with actual schema name before execution
-- 2. Replace %%TENANT_ID%% with actual tenant ID before execution
-- 3. Execute within a transaction block for additional safety if desired
-- 4. Review all verification query results to confirm expected outcomes
-- 5. Monitor the progress indicators and result messages during execution
--
-- ================================================================================
